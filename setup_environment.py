#!/usr/bin/env python3
"""
Setup script for Tower Defense Game Environment
This script helps verify that all requirements are properly installed.
"""

import sys
import subprocess
import importlib

def check_package(package_name, import_name=None):
    """Check if a package is installed and importable"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        print(f"✅ {package_name} - Version: {version}")
        return True
    except ImportError:
        print(f"❌ {package_name} - NOT INSTALLED")
        return False

def main():
    """Main setup verification"""
    print("=== Tower Defense Game Environment Setup ===")
    print()
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major == 3 and python_version.minor >= 9:
        print("✅ Python version is compatible")
    else:
        print("❌ Python version should be 3.9 or higher")
        return False
    
    print()
    print("Checking required packages:")
    print("-" * 40)
    
    # Check required packages
    packages = [
        ("pygame", "pygame"),
        ("openai", "openai")
    ]
    
    all_installed = True
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            all_installed = False
    
    print()
    print("-" * 40)
    
    if all_installed:
        print("🎉 All requirements are installed and ready!")
        print()
        print("To run the game:")
        print("1. Activate the conda environment: conda activate tower_defense")
        print("2. Run the launcher: python game_launcher.py")
        print()
        print("To deactivate the environment: conda deactivate")
        return True
    else:
        print("❌ Some packages are missing. Please install them:")
        print("conda activate tower_defense")
        print("conda install pygame -c conda-forge")
        print("pip install openai>=1.0.0")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 