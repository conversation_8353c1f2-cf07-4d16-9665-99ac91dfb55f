#!/usr/bin/env python3
"""
Tower Defense Game Launcher
Foundation system for choosing configurations and automatic level generation
"""

import os
import sys
import json
import pygame
from typing import List, Dict, Any, Optional
from datetime import datetime
import time

# Import the new modular launcher components
from launcher.configuration_manager import ConfigurationManager
from launcher.event_handler import EventHandler
from launcher.navigation_manager import NavigationManager
from launcher.launcher_renderer import LauncherRenderer

class GameLauncher:
    """Main launcher for the tower defense game with configuration selection and adaptive level generation"""
    
    def __init__(self):
        """Initialize the game launcher"""
        pygame.init()

        # Screen setup
        self.SCREEN_WIDTH = 1000
        self.SCREEN_HEIGHT = 800
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("Tower Defense - Game Launcher")

        # Game loop settings
        self.clock = pygame.time.Clock()
        self.FPS = 60

        # Initialize modular components
        self.config_manager = ConfigurationManager()
        self.navigation_manager = NavigationManager()
        self.event_handler = EventHandler(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.renderer = LauncherRenderer(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)

        # Initialize systems needed for configuration loading
        from game_systems.global_upgrade_system import GlobalUpgradeSystem
        from game_systems.global_upgrade_ui import GlobalUpgradeUI
        from game_systems.variant_selection_ui import VariantSelectionUI
        from game_systems.level_variant_generator import LevelVariantGenerator

        self.global_upgrade_system = GlobalUpgradeSystem()
        self.global_upgrade_ui = GlobalUpgradeUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.global_upgrade_system)
        self.variant_ui = VariantSelectionUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.variant_generator = LevelVariantGenerator()

        # State
        self.running = True

        # Set up event handler callbacks
        self._setup_event_callbacks()

        # Load available configurations
        self.config_manager.load_configurations()
        self.navigation_manager.update_scroll_limits(len(self.config_manager.configs))

        # Load performance statistics
        self.load_performance_stats()

    def _setup_event_callbacks(self):
        """Set up event handler callbacks"""
        self.event_handler.on_generate_config = self.generate_multi_game_adaptive_config
        self.event_handler.on_toggle_stats = self.navigation_manager.toggle_performance_panel
        self.event_handler.on_toggle_upgrades = self._toggle_upgrade_menu
        self.event_handler.on_create_variant = self.handle_variant_creation
        self.event_handler.on_launch_game = self.launch_game
        self.event_handler.on_navigate_to_main = self.navigation_manager.navigate_to_main
        self.event_handler.on_navigate_to_variants = self.navigation_manager.navigate_to_variants
        self.event_handler.on_navigate_to_level_options = self.navigation_manager.navigate_to_level_options
        self.event_handler.on_open_level_preview = self.navigation_manager.open_level_preview
        self.event_handler.on_close_level_preview = self.navigation_manager.close_level_preview
        self.event_handler.on_variant_creation = self.handle_variant_creation
        self.event_handler.on_dismiss_status = self.navigation_manager.clear_status_message

    def _toggle_upgrade_menu(self):
        """Toggle the upgrade menu"""
        self.navigation_manager.toggle_upgrade_menu()
        if self.navigation_manager.show_upgrade_menu:
            self.global_upgrade_ui.open_menu()
    


    
    def load_performance_stats(self):
        """Load and analyze recent performance statistics"""
        try:
            from ai.adaptive_config_generator import load_all_recent_performances
            
            # Load recent performances
            performances = load_all_recent_performances()
            
            if not performances:
                self.recent_performance_stats = None
                return
            
            # Calculate aggregate statistics
            scores = [p.score for p in performances]
            avg_score = sum(scores) / len(scores)
            win_rate = sum(1 for p in performances if p.win_flag) / len(performances) * 100
            
            # Get performance trend (improving/declining)
            trend = "stable"
            if len(scores) >= 3:
                recent_avg = sum(scores[:2]) / 2  # Last 2 games
                older_avg = sum(scores[2:]) / len(scores[2:])  # Older games
                if recent_avg > older_avg + 10:
                    trend = "improving"
                elif recent_avg < older_avg - 10:
                    trend = "declining"
            
            # Create performance history for display
            performance_history = []
            for i, perf in enumerate(performances):
                performance_history.append({
                    'game_number': i + 1,
                    'score': perf.score,
                    'won': perf.win_flag,
                    'lives_remaining_pct': (perf.lives_remaining / perf.starting_lives) * 100,
                    'towers_used': len([t for t, c in perf.towers_built.items() if c > 0])
                })
            
            self.recent_performance_stats = {
                'games_count': len(performances),
                'average_score': avg_score,
                'win_rate': win_rate,
                'trend': trend,
                'performance_history': performance_history,
                'latest_score': scores[0] if scores else 0
            }
            
        except Exception as e:
            print(f"Error loading performance stats: {e}")
            self.recent_performance_stats = None
    




    
    def handle_events(self):
        """Handle launcher events"""
        events = self.event_handler.handle_events(self.navigation_manager, self.config_manager, self.variant_ui, self.global_upgrade_ui)

        for event_type in events:
            if event_type[0] == 'quit':
                self.running = False


    
    def handle_variant_creation(self):
        """Handle creation of a new variant"""
        try:
            variant = self.variant_ui.get_selected_variant()
            if variant:
                # The variant is already created and saved by the UI
                print(f"Variant created: {variant.variant_name}")
                print(f"Difficulty: {variant.total_difficulty_multiplier:.1f}x")
                print(f"Reward: {variant.total_reward_multiplier:.1f}x")
                print(f"Path: {variant.config_path}")
                
                # Close variant selector
                self.navigation_manager.close_variant_selector()
                self.variant_ui.close()

                # Reload configurations to include the new variant
                self.config_manager.load_configurations()
                self.navigation_manager.update_scroll_limits(len(self.config_manager.configs))

                # Show status message
                self.navigation_manager.show_status_message(f"✅ Variant created: {variant.variant_name}")
                
        except Exception as e:
            print(f"Error creating variant: {e}")
            self.navigation_manager.show_status_message(f"❌ Error creating variant: {e}")

    
    def launch_game(self, config_info: Dict):
        """Launch the game with the selected configuration"""
        print(f"Launching game with config: {config_info['name']}")
        
        # Set the config file for the game to use
        from config.game_config import set_config_file
        set_config_file(config_info['path'])
        
        # Import and run the game
        from game import Game
        
        # Close launcher window
        pygame.display.quit()
        
        # Run the game (skip config selection since launcher already set it)
        game = Game(skip_config_selection=True, launched_from_menu=True)
        # Pass the global upgrade system to the game
        game.global_upgrade_system = self.global_upgrade_system
        game.run()
        
        # Game finished - check if we should generate adaptive config
        self.handle_post_game()
        
        # Reinitialize pygame for launcher
        pygame.display.init()
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("Tower Defense - Game Launcher")
        
        # Reload configurations and performance stats (in case new ones were generated)
        self.config_manager.load_configurations()
        self.navigation_manager.update_scroll_limits(len(self.config_manager.configs))
        self.load_performance_stats()
    
    def handle_post_game(self):
        """Handle actions after game completion"""
        # Check for new performance data using completion marker
        performance_dir = os.path.join("ai", "performance_data")
        marker_path = os.path.join(performance_dir, "last_completion.txt")
        
        if os.path.exists(marker_path):
            try:
                # Check if this is a new completion since we last checked
                with open(marker_path, 'r') as f:
                    lines = f.read().strip().split('\n')
                    if len(lines) >= 2:
                        completion_timestamp = lines[0]
                        performance_filename = lines[1]
                        
                        # Check if we've already processed this completion
                        processed_marker_path = os.path.join(performance_dir, "last_processed.txt")
                        should_process = True
                        
                        if os.path.exists(processed_marker_path):
                            with open(processed_marker_path, 'r') as pf:
                                last_processed = pf.read().strip()
                                if last_processed == completion_timestamp:
                                    should_process = False  # Already processed this completion
                        
                        if should_process:
                            print(f"Detected new game completion at {completion_timestamp}")
                            print(f"Performance saved as: {performance_filename}")
                            
                            # Mark this completion as processed
                            with open(processed_marker_path, 'w') as pf:
                                pf.write(completion_timestamp)
                            
                            # Automatically generate adaptive config using multi-game analysis
                            self.auto_generate_multi_game_config()
                        else:
                            print("No new game completions detected")
                            
            except Exception as e:
                print(f"Error checking completion marker: {e}")
        else:
            print("No completion marker found - no recent game completions")
    
    def auto_generate_multi_game_config(self):
        """Generate adaptive config based on up to 5 recent games (recommended)"""
        try:
            print("DEBUG: Starting generate_multi_game_adaptive_config (button method)")
            from ai.adaptive_config_generator import AdaptiveConfigGenerator
            
            print(f"DEBUG: recent_performance_stats: {self.recent_performance_stats}")
            if not self.recent_performance_stats:
                print("DEBUG: No performance data found")
                # Note: This is auto-generation, so we don't show status messages to avoid interrupting the user
                return
            
            print(f"DEBUG: Generating multi-game adaptive config from {self.recent_performance_stats['games_count']} recent games...")
            
            # Generate adaptive config using multi-game analysis with Modular AI ONLY (testing)
            print("DEBUG: Creating AdaptiveConfigGenerator...")
            generator = AdaptiveConfigGenerator(use_modular_ai=True, use_full_ai=False)
            print("DEBUG: Calling generator.generate_config_from_recent_games()...")
            config = generator.generate_config_from_recent_games()
            print(f"DEBUG: Generator returned: {config is not None}")
            
            if config:
                avg_score = self.recent_performance_stats['average_score']
                trend = self.recent_performance_stats['trend']
                method = config.get('_adaptive_metadata', {}).get('generation_method', 'unknown')
                # Note: This is auto-generation, so we don't show status messages to avoid interrupting the user
                print(f"DEBUG: Generated config with method: {method}")

                # Reload configurations and performance stats
                print("DEBUG: Reloading configurations...")
                self.config_manager.load_configurations()
                self.navigation_manager.update_scroll_limits(len(self.config_manager.configs))
                print("DEBUG: Reloading performance stats...")
                self.load_performance_stats()
                print(f"Multi-game adaptive configuration generated (Avg: {avg_score:.1f}%, Trend: {trend})")
            else:
                print("DEBUG: Generator returned None - generation failed")

        except Exception as e:
            print(f"DEBUG: Exception in generate_multi_game_adaptive_config: {e}")
            import traceback
            traceback.print_exc()
            print(f"Error generating adaptive config: {e}")
    
    def generate_multi_game_adaptive_config(self):
        """Generate adaptive config based on up to 5 recent games (recommended)"""
        try:
            print("DEBUG: Starting generate_multi_game_adaptive_config (button method)")
            from ai.adaptive_config_generator import AdaptiveConfigGenerator
            
            print(f"DEBUG: recent_performance_stats: {self.recent_performance_stats}")
            if not self.recent_performance_stats:
                print("DEBUG: No performance data found")
                self.navigation_manager.show_status_message("❌ No performance data found. Play some games first!")
                return
            
            print(f"DEBUG: Generating multi-game adaptive config from {self.recent_performance_stats['games_count']} recent games...")
            
            # Generate adaptive config using multi-game analysis with Modular AI ONLY (testing)
            print("DEBUG: Creating AdaptiveConfigGenerator...")
            generator = AdaptiveConfigGenerator(use_modular_ai=True, use_full_ai=False)
            print("DEBUG: Calling generator.generate_config_from_recent_games()...")
            config = generator.generate_config_from_recent_games()
            print(f"DEBUG: Generator returned: {config is not None}")
            
            if config:
                avg_score = self.recent_performance_stats['average_score']
                trend = self.recent_performance_stats['trend']
                method = config.get('_adaptive_metadata', {}).get('generation_method', 'unknown')
                method_emoji = "🧩" if 'modular_ai' in method else "🤖" if 'ai' in method else "🔄"
                self.navigation_manager.show_status_message(f"✅ {method_emoji} AI level generated! Avg: {avg_score:.1f}%, Trend: {trend}")
                print(f"DEBUG: Generated config with method: {method}")

                # Reload configurations and performance stats
                print("DEBUG: Reloading configurations...")
                self.config_manager.load_configurations()
                self.navigation_manager.update_scroll_limits(len(self.config_manager.configs))
                print("DEBUG: Reloading performance stats...")
                self.load_performance_stats()
                print(f"Multi-game adaptive configuration generated (Avg: {avg_score:.1f}%, Trend: {trend})")
            else:
                print("DEBUG: Generator returned None - generation failed")
                self.navigation_manager.show_status_message("❌ Failed to generate adaptive config")
                
        except Exception as e:
            print(f"Error generating adaptive config: {e}")
            self.navigation_manager.show_status_message(f"❌ Error generating adaptive config: {e}")
    
    def draw(self):
        """Draw the elegant launcher interface"""
        self.renderer.draw(
            self.screen,
            self.navigation_manager,
            self.config_manager,
            self.event_handler,
            self.recent_performance_stats,
            self.variant_ui,
            self.global_upgrade_ui
        )








    
    def run(self):
        """Main launcher loop"""
        while self.running:
            self.handle_events()
            self.draw()
            self.clock.tick(self.FPS)
            
            # Clear status message after 5 seconds
            if self.navigation_manager.show_generation_status:
                if not hasattr(self, '_status_timer'):
                    self._status_timer = time.time()
                elif time.time() - self._status_timer > 5:
                    self.navigation_manager.clear_status_message()
                    delattr(self, '_status_timer')
        
        pygame.quit()
        sys.exit()


def main():
    """Main entry point"""
    print("=== Tower Defense Game Launcher ===")
    launcher = GameLauncher()
    launcher.run()


if __name__ == "__main__":
    main() 